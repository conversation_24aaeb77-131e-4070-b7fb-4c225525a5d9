// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:gap/gap.dart';
// import 'package:storetrack_app/config/themes/app_colors.dart';
// import 'package:storetrack_app/core/extensions/theme_extensions.dart';
// import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
//     as entities;
// import 'package:storetrack_app/config/routes/app_router.gr.dart';
// import 'package:storetrack_app/features/home/<USER>/widgets/question_indicators.dart';

// class QuestionCard extends StatelessWidget {
//   final entities.Question question;
//   final double progress;
//   final String progressText;
//   final bool isMandatory;
//   final bool showCameraIcon;
//   final bool isCameraMandatory;
//   final bool hasPhotoUrl;

//   const QuestionCard({
//     super.key,
//     required this.question,
//     required this.progress,
//     required this.progressText,
//     required this.isMandatory,
//     required this.showCameraIcon,
//     required this.isCameraMandatory,
//     required this.hasPhotoUrl,
//   });

//   @override
//   Widget build(BuildContext context) {
//     // Check if this is a photo question
//     final isPhotoQuestion = showCameraIcon || hasPhotoUrl;
//     final subtitle = _getSubtitle();
//     final tag = _getTag();

//     return Stack(
//       children: [
//         Container(
//           margin: const EdgeInsets.symmetric(vertical: 4.0),
//           padding: const EdgeInsets.all(16.0),
//           decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(8),
//             boxShadow: [
//               BoxShadow(
//                 color: AppColors.black10,
//                 blurRadius: 4,
//                 offset: const Offset(0, 2),
//               ),
//             ],
//           ),
//           child: InkWell(
//             borderRadius: BorderRadius.circular(8),
//             onTap: () => _handleQuestionTap(context),
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // Progress bar and text row
//                 Row(
//                   children: [
//                     Expanded(
//                       child: Container(
//                         height: 4,
//                         decoration: BoxDecoration(
//                           color: AppColors.lightGrey2,
//                           borderRadius: BorderRadius.circular(2),
//                         ),
//                         child: FractionallySizedBox(
//                           alignment: Alignment.centerLeft,
//                           widthFactor: progress.clamp(0.0, 1.0),
//                           child: Container(
//                             decoration: BoxDecoration(
//                               color: AppColors.primaryBlue,
//                               borderRadius: BorderRadius.circular(2),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                     const Gap(12),
//                     Text(
//                       progressText,
//                       style: Theme.of(context).textTheme.bodySmall?.copyWith(
//                             color: AppColors.blackTint1,
//                             fontSize: 12,
//                             fontWeight: FontWeight.w500,
//                           ),
//                     ),
//                   ],
//                 ),
//                 const Gap(12),
//                 // Main content row
//                 Row(
//                   children: [
//                     Expanded(
//                       child: Column(
//                         crossAxisAlignment: CrossAxisAlignment.start,
//                         children: [
//                           Text(
//                             question.questionDescription ?? 'Unnamed Question',
//                             style: Theme.of(context)
//                                 .textTheme
//                                 .montserratTitleExtraSmall,
//                           ),
//                           if (subtitle != null) ...[
//                             const Gap(4),
//                             Text(
//                               subtitle,
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .bodySmall
//                                   ?.copyWith(
//                                     color: AppColors.blackTint1,
//                                   ),
//                             ),
//                           ],
//                         ],
//                       ),
//                     ),
//                     const Gap(16),
//                     // Right side content
//                     Row(
//                       mainAxisSize: MainAxisSize.min,
//                       children: [
//                         if (tag != null) ...[
//                           Container(
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 8, vertical: 4),
//                             decoration: BoxDecoration(
//                               color: AppColors.lightGrey2,
//                               borderRadius: BorderRadius.circular(4),
//                             ),
//                             child: Text(
//                               tag,
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .bodySmall
//                                   ?.copyWith(
//                                     color: AppColors.blackTint1,
//                                     fontSize: 10,
//                                     fontWeight: FontWeight.w500,
//                                   ),
//                             ),
//                           ),
//                           const Gap(8),
//                         ],
//                         if (isPhotoQuestion) ...[
//                           const Icon(
//                             Icons.camera_alt,
//                             size: 20,
//                             color: AppColors.blackTint1,
//                           ),
//                         ],
//                       ],
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ),
//         ),
//         // Position indicators on top of the card
//         Positioned.fill(
//           child: QuestionIndicators(
//             question: question,
//             isMandatory: isMandatory,
//             showCameraIcon: showCameraIcon,
//             isCameraMandatory: isCameraMandatory,
//             hasPhotoUrl: hasPhotoUrl,
//           ),
//         ),
//       ],
//     );
//   }

//   String? _getSubtitle() {
//     // Check if this is a photo question
//     if (showCameraIcon || hasPhotoUrl) {
//       return 'Full display';
//     }
//     return null;
//   }

//   String? _getTag() {
//     // Check if this is a photo question with mandatory camera
//     if ((showCameraIcon || hasPhotoUrl) && isCameraMandatory) {
//       return 'HI-RES';
//     }
//     return null;
//   }

//   void _handleQuestionTap(BuildContext context) {
//     final questionParts = question.questionParts ?? [];
//     final hasSignature = question.hasSignature ?? false;
//     final isMulti = question.isMulti ?? false;

//     if (questionParts.length != 1 || hasSignature) {
//       if (isMulti) {
//         AutoRouter.of(context).push(const FQPDRoute());
//       } else {
//         AutoRouter.of(context).push(SubHeaderRoute(
//           title: question.questionDescription ?? 'Details',
//           questionParts: questionParts,
//           question: question,
//         ));
//       }
//     } else {
//       AutoRouter.of(context).push(QPMDRoute(question: question));
//     }
//   }
// }

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
// If 'theme_extensions.dart' defines montserratTitleExtraSmall or other specific text styles,
// ensure they are accessible or define similar styles directly.
// import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/question_indicators.dart';

class QuestionCard extends StatelessWidget {
  final entities.Question question;
  final double progress;
  final String progressText;
  final bool isMandatory;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final bool hasPhotoUrl;

  const QuestionCard({
    super.key,
    required this.question,
    required this.progress,
    required this.progressText,
    required this.isMandatory,
    required this.showCameraIcon,
    required this.isCameraMandatory,
    required this.hasPhotoUrl,
  });

  // Helper to get subtitle for the "Add photos" section, similar to original _getSubtitle
  String? _getPhotoSectionSubtitle() {
    if (showCameraIcon || hasPhotoUrl) {
      return 'Full display';
    }
    return null;
  }

  // Helper to get tag for the "Add photos" section, similar to original _getTag
  String? _getPhotoSectionTag() {
    if ((showCameraIcon || hasPhotoUrl) && isCameraMandatory) {
      return 'HI-RES';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final photoSectionSubtitle = _getPhotoSectionSubtitle();
    final photoSectionTag = _getPhotoSectionTag();

    // Define text styles based on the image and typical app theming
    // Main Title Style (e.g., "Multipack Stickering Task")
    final titleTextStyle =
        Theme.of(context).textTheme.montserratTitleExtraSmall;

    // Progress Text Style (e.g., "0 of 5")
    final progressTextStyle = Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppColors.black.withOpacity(0.6),
        );
    //  Theme.of(context).textTheme.bodyLarge?.copyWith(
    //   ---
    //           // bodyLarge is typically 16sp
    //           color: AppColors.blackTint1, // Use a grey color from AppColors
    //           fontWeight: FontWeight.w500,
    //           fontSize: 16, // As per image
    //         ) ??
    // TextStyle(
    //   // Fallback
    //   color: Colors.grey.shade600,
    //   fontWeight: FontWeight.w500,
    //   fontSize: 16,
    // );

    // "Add photos" Title Style
    final addPhotosTitleStyle =
        Theme.of(context).textTheme.montserratTitleExtraSmall;

    // "Full display" Subtitle Style
    final addPhotosSubtitleStyle =
        Theme.of(context).textTheme.montserratTableSmall; // Fallback

    // "HI-RES" Tag Text Style
    // Using a darker grey or black for better contrast on lightGrey2 background
    final hiResTagTextStyle = Theme.of(context).textTheme.labelMedium?.copyWith(
              // labelMedium is typically 12sp
              fontWeight: FontWeight.bold,
              color:
                  AppColors.black.withOpacity(0.75), // Darker grey for contrast
              fontSize: 12,
            ) ??
        TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.black.withOpacity(0.75));

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(
                12.0), // Slightly more rounded corners for the main card
            boxShadow: [
              BoxShadow(
                color: AppColors
                    .black10, // Assuming this is a light shadow color like Colors.black.withOpacity(0.1)
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            borderRadius:
                BorderRadius.circular(12.0), // Match container's border radius
            onTap: () => _handleQuestionTap(context),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 1. Title (e.g., "Multipack Stickering Task")
                Text(
                  question.questionDescription ?? 'Unnamed Question',
                  style: titleTextStyle,
                ),
                const Gap(12), // Spacing after title

                // 2. Progress bar and text row (e.g., "0 of 5")
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 8, // Thicker progress bar
                        clipBehavior: Clip
                            .antiAlias, // Ensures rounded corners are respected by child
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey2, // Background of the bar
                          borderRadius: BorderRadius.circular(
                              4), // Rounded ends for the bar
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: progress.clamp(0.0, 1.0),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.primaryBlue, // Progress color
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const Gap(16), // Space between bar and text
                    Text(
                      progressText,
                      style: progressTextStyle,
                    ),
                  ],
                ),

                // Conditionally add space only if the "Add photos" section will be shown
                if (showCameraIcon) const Gap(20),

                // 3. "Add photos" section (conditional based on showCameraIcon)
                if (showCameraIcon)
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0,
                        vertical: 20.0), // Inner padding for this box
                    decoration: BoxDecoration(
                      color: Colors.white, // Background of the inner box
                      borderRadius: BorderRadius.circular(
                          12.0), // Rounded corners for this box
                      border: Border.all(
                          color: AppColors.lightGrey2.withOpacity(0.8),
                          width: 1), // Border for the box
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Left side: "Add photos", "Full display"
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Add photos",
                                style: addPhotosTitleStyle,
                              ),
                              if (photoSectionSubtitle != null) ...[
                                const Gap(4),
                                Text(
                                  photoSectionSubtitle,
                                  style: addPhotosSubtitleStyle,
                                ),
                              ],
                            ],
                          ),
                        ),
                        const Gap(16), // Space before right-side elements

                        // Right side: HI-RES tag and Camera Icon
                        if (photoSectionTag != null) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 5),
                            decoration: BoxDecoration(
                              color: AppColors
                                  .lightGrey2, // Background for HI-RES tag
                              borderRadius:
                                  BorderRadius.circular(16), // Pill shape
                            ),
                            child: Text(
                              photoSectionTag,
                              style: hiResTagTextStyle,
                            ),
                          ),
                          const Gap(12), // Space between tag and icon
                        ],
                        const Icon(
                          Icons
                              .camera_alt_outlined, // Outlined camera icon from image
                          size: 28, // Icon size from image
                          color: AppColors.black, // Icon color from image
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
        // Position indicators on top of the card (e.g., "Mandatory")
        // This part remains unchanged and will be layered on top.
        Positioned.fill(
          child: QuestionIndicators(
            question: question,
            isMandatory: isMandatory,
            showCameraIcon:
                showCameraIcon, // This refers to the top-left indicator
            isCameraMandatory: isCameraMandatory,
            hasPhotoUrl: hasPhotoUrl,
          ),
        ),
      ],
    );
  }

  // _handleQuestionTap remains the same as in the original code
  void _handleQuestionTap(BuildContext context) {
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final isMulti = question.isMulti ?? false;

    if (questionParts.length != 1 || hasSignature) {
      if (isMulti) {
        AutoRouter.of(context).push(FQPDRoute(question: question));
      } else {
        AutoRouter.of(context).push(SubHeaderRoute(
          title: question.questionDescription ?? 'Details',
          questionParts: questionParts,
          question: question,
        ));
      }
    } else {
      AutoRouter.of(context).push(QPMDRoute(question: question));
    }
  }
}
