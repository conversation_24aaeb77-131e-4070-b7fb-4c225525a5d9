import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';


class PhotoUploadWidget extends StatefulWidget {
  final VoidCallback? onCameraPressed;
  final VoidCallback? onPhotoPressed;
  final List<String>? selectedImages; // List of image paths
  final Function(List<String>)? onImagesChanged; // Callback when images change
  final VoidCallback? onSelectImage; // Callback for selecting images

  const PhotoUploadWidget({
    super.key,
    this.onCameraPressed,
    this.onPhotoPressed,
    this.selectedImages,
    this.onImagesChanged,
    this.onSelectImage,
  });

  @override
  State<PhotoUploadWidget> createState() => _PhotoUploadWidgetState();
}

class _PhotoUploadWidgetState extends State<PhotoUploadWidget> {
  bool _isPressed = false;
  final ImagePicker _picker = ImagePicker();
  List<String> _images = [];

  @override
  void initState() {
    super.initState();
    _images = List.from(widget.selectedImages ?? []);
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
      if (image != null) {
        setState(() {
          _images.add(image.path);
        });
        // Notify parent about the change
        widget.onImagesChanged?.call(_images);
        widget.onSelectImage?.call();
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.camera);
      if (image != null) {
        setState(() {
          _images.add(image.path);
        });
        // Notify parent about the change
        widget.onImagesChanged?.call(_images);
        widget.onCameraPressed?.call();
      }
    } catch (e) {
      print('Error taking photo: $e');
    }
  }

  void _showImageSourceOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                _pickImageFromCamera();
              },
            ),
            // ListTile(
            //   leading: const Icon(Icons.photo_library),
            //   title: const Text('Choose from Gallery'),
            //   onTap: () {
            //     Navigator.pop(context);
            //     _pickImage();
            //   },
            // ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
    //    if (_images.isNotEmpty) async{
    //   final imagePaths = await ImageStorageUtils.saveImageToAppStorage(_images);
    //   if (imagePaths != null) {
    //     // Store the image paths in the Realm database
    //     final realm = Realm();
    //     final taskDetailModel = TaskDetailModel();
    //     taskDetailModel.imagePaths = imagePaths;
    //     realm.write(() {
    //       realm.add(taskDetailModel);
    //     });
    //   }
    // }
  }

  @override
  Widget build(BuildContext context) {
    // Check if images are available
    bool hasImages = _images.isNotEmpty;

    return Container(
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                // Left side content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('Add photos',
                          style:
                              TextTheme.of(context).montserratTitleExtraSmall),
                      const SizedBox(height: 4),
                      Text('Clearly show each location',
                          style: TextTheme.of(context).montserratTableSmall),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Right side buttons
                Row(
                  children: [
                    // HI-RES button
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 6,
                      ),
                      child: Text('HI-RES',
                          style: TextTheme.of(context)
                              .montserratTitleSmall
                              .copyWith(
                                  fontWeight: FontWeight.w600, fontSize: 10)),
                    ),

                    const SizedBox(width: 12),

                    // Camera/Select button
                    GestureDetector(
                      onTapDown: (_) => setState(() => _isPressed = true),
                      onTapUp: (_) => setState(() => _isPressed = false),
                      onTapCancel: () => setState(() => _isPressed = false),
                      onTap: _showImageSourceOptions,
                      child: Icon(
                        Icons.camera_alt_outlined,
                        color: Colors.grey.shade700,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // Image display section (only show if images are available)
            if (hasImages) ...[
              const SizedBox(height: 16),
              // Divider line
              Container(
                height: 1,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              // Image display row
              GestureDetector(
                onTap: _showImageSourceOptions,
                child: Row(
                  children: [
                    // This Expanded widget is crucial. It allows the image list
                    // to take up all the available horizontal space in the Row.
                    Expanded(
                      child: _images.isEmpty
                          // --- WIDGET TO SHOW WHEN NO IMAGES ARE SELECTED ---
                          ? Container(
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Center(
                                child: Text(
                                  'Tap to add images',
                                  style: TextStyle(color: Colors.grey.shade600),
                                ),
                              ),
                            )
                          // --- WIDGET TO SHOW WHEN IMAGES ARE PRESENT ---
                          : SizedBox(
                              height:
                                  60, // This gives the horizontal ListView a fixed height.
                              child: ListView.builder(
                                scrollDirection: Axis
                                    .horizontal, // Makes the list scroll horizontally.
                                itemCount: _images.length,
                                itemBuilder: (BuildContext context, int index) {
                                  // Add padding to the right of each item except the last one.
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: Container(
                                      width: 60, // The width of each thumbnail.
                                      height:
                                          60, // The height of each thumbnail.
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        image: DecorationImage(
                                          // Use the image at the current index.
                                          image:
                                              FileImage(File(_images[index])),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                    ),
                    const SizedBox(width: 12),
                    // Arrow icon
                    GestureDetector(
                      onTap: () {
                        print('sample------');
                        context.router.push(MPTRoute(
                          images: _images,
                          taskId: '123',
                          formId: '456',
                          questionId: '789',
                          questionPartId: '101',
                          measurementId: '112',
                          combineTypeId: '131',
                          questionPartMultiId: '415',
                        ));
                      },
                      child: Icon(
                        Icons.chevron_right,
                        color: Colors.grey.shade600,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ],
        ),
      ),
    );
  }
}

// Alternative version with File/NetworkImage support
class PhotoUploadWidgetWithFile extends StatefulWidget {
  final VoidCallback? onCameraPressed;
  final VoidCallback? onPhotoPressed;
  final List<dynamic>?
      selectedImages; // Can be File, String (path), or network URL
  final VoidCallback? onSelectImage;

  const PhotoUploadWidgetWithFile({
    super.key,
    this.onCameraPressed,
    this.onPhotoPressed,
    this.selectedImages,
    this.onSelectImage,
  });

  @override
  State<PhotoUploadWidgetWithFile> createState() =>
      _PhotoUploadWidgetWithFileState();
}

class _PhotoUploadWidgetWithFileState extends State<PhotoUploadWidgetWithFile> {
  bool _isPressed = false;

  ImageProvider _getImageProvider(dynamic image) {
    if (image is String) {
      // Check if it's a network URL or local path
      if (image.startsWith('http')) {
        return NetworkImage(image);
      } else {
        return AssetImage(image); // or FileImage(File(image)) for file paths
      }
    }
    // Handle File type if needed
    // else if (image is File) {
    //   return FileImage(image);
    // }

    return const AssetImage('assets/placeholder.png'); // fallback
  }

  @override
  Widget build(BuildContext context) {
    bool hasImages =
        widget.selectedImages != null && widget.selectedImages!.isNotEmpty;

    return Container(
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text('Add photos',
                          style:
                              TextTheme.of(context).montserratTitleExtraSmall),
                      const SizedBox(height: 4),
                      Text('Clearly show each location',
                          style: TextTheme.of(context).montserratTableSmall),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 6,
                      ),
                      child: Text('HI-RES',
                          style: TextTheme.of(context)
                              .montserratTitleSmall
                              .copyWith(
                                  fontWeight: FontWeight.w600, fontSize: 10)),
                    ),
                    const SizedBox(width: 12),
                    GestureDetector(
                      onTapDown: (_) => setState(() => _isPressed = true),
                      onTapUp: (_) => setState(() => _isPressed = false),
                      onTapCancel: () => setState(() => _isPressed = false),
                      onTap: widget.onSelectImage ?? widget.onCameraPressed,
                      child: Icon(
                        Icons.camera_alt_outlined,
                        color: Colors.grey.shade700,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (hasImages) ...[
              const SizedBox(height: 16),
              Container(
                height: 1,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              GestureDetector(
                onTap: widget.onSelectImage,
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        image: DecorationImage(
                          image:
                              _getImageProvider(widget.selectedImages!.first),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Expanded(
                    //   child: Row(
                    //     children: [
                    //       Icon(
                    //         Icons.menu,
                    //         color: Colors.grey.shade600,
                    //         size: 20,
                    //       ),

                    const Spacer(),

                    Icon(
                      Icons.chevron_right,
                      color: Colors.grey.shade600,
                      size: 24,
                      //     ),
                      //   ],
                      // ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
